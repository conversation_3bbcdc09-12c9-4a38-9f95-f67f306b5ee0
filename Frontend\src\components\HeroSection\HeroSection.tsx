'use client';
import { useState } from 'react';
import VideoModal from '@components/VideoModal';
import { Container } from 'react-bootstrap';
import Link from 'next/link';
import Breadcrumb from '@components/Breadcrumb';
import Heading from '@components/Heading';
import CircularButtonWithArrow from '@components/CircularButtonWithArrow';
import styles from './HeroSection.module.css';
import Button from '@components/Button';
import ImageWithBlurPreview from '@components/ImageWithBlurPreview';

export default function HeroSection({
  heroData,
  variant,
  L2pageName,
  L2pageSlug,
  L3pageName,
  L3pageSlug,
  industrySlug,
  industryName,
  resources_page = false,
  cloud_page = false,
}: any) {
  const [show, setShow] = useState(false);
  const [showClose, setShowClose] = useState(false);
  const [videoLink, setVideoLink] = useState('');

  return (
    <>
      {variant === 'primary' && (
        <Container fluid className={styles.main_container}>
          <ImageWithBlurPreview
            data={heroData?.image?.data?.attributes}
            mobileData={heroData?.mobile_image?.data?.attributes}
            fill={true}
            quality={95}
            priority={true}
            loading="eager"
            mainClass={styles.background_image}
          />

          <div
            className={
              resources_page === true
                ? styles.inner_container_resources
                : styles.inner_container
            }
          >
            {!cloud_page && (
              <Breadcrumb
                L2pageName={L2pageName}
                L2pageSlug={L2pageSlug}
                L3pageName={L3pageName}
                L3pageSlug={L3pageSlug}
                industrySlug={industrySlug}
                industryName={industryName}
              />
            )}
            <div
              className={
                cloud_page === true
                  ? styles.section_without_breadcrumbs
                  : styles.section
              }
            >
              <Heading
                headingType="h1"
                title={heroData?.title}
                className={styles.title}
              />
              {heroData?.description && (
                <div
                  className={styles.hero_desc}
                  dangerouslySetInnerHTML={{ __html: heroData?.description }}
                ></div>
              )}
              {cloud_page && heroData?.link && (
                <>
                  <Button
                    className={styles.btn}
                    onClick={() => {
                      setVideoLink(heroData?.link);
                      setShow(true);
                      setShowClose(true);
                    }}
                  >
                    View Demo
                  </Button>
                  <VideoModal
                    show={show}
                    setShow={setShow}
                    setShowClose={setShowClose}
                    showClose={showClose}
                    videoLink={videoLink}
                  />
                </>
              )}
            </div>
          </div>
        </Container>
      )}
      {variant === 'about_us' && (
        <Container fluid className={styles.main_container}>
          <ImageWithBlurPreview
            data={heroData?.image?.data?.attributes}
            mobileData={heroData?.mobile_image?.data?.attributes}
            fill={true}
            quality={95}
            priority={true}
            loading="eager"
            mainClass={styles.background_image}
          />
          <div className={styles.inner_container}>
            <span></span>
            <div className={styles.section}>
              <div className={styles.tag}>{heroData?.hero_tag}</div>
              <Heading
                headingType="h1"
                richTextValue={heroData?.title}
                className={styles.title}
              />
              <div
                className={styles.hero_desc}
                dangerouslySetInnerHTML={{ __html: heroData?.description }}
              ></div>
            </div>
          </div>
        </Container>
      )}
      {variant === 'partners' && (
        <Container fluid className={styles.main_container}>
          <ImageWithBlurPreview
            data={heroData?.image?.data?.attributes}
            mobileData={heroData?.mobile_image?.data?.attributes}
            fill={true}
            quality={95}
            priority={true}
            loading="eager"
            mainClass={styles.background_image}
          />
          <div className={styles.inner_container_partners}>
            <div className={styles.section}>
              <Heading
                headingType="h1"
                title={heroData?.title}
                className={styles.title}
              />
              {heroData?.description && (
                <div
                  className={styles.hero_desc}
                  dangerouslySetInnerHTML={{ __html: heroData?.description }}
                ></div>
              )}
              <Link
                href={heroData?.link}
                className={styles.circular_button_mobile}
              >
                <CircularButtonWithArrow variant="medium" />
              </Link>
            </div>
          </div>
        </Container>
      )}
      {variant === 'ai-readiness' && (
        <Container fluid className={styles.main_container}>
          <ImageWithBlurPreview
            data={heroData?.image?.data?.attributes}
            mobileData={heroData?.mobile_image?.data?.attributes}
            fill={true}
            quality={95}
            priority={true}
            loading="eager"
            mainClass={styles.background_image}
          />
          <div className={styles.inner_container_ai_readiness}>
            <div className={styles.section}>
              <Heading
                headingType="h1"
                title={heroData?.title}
                className={styles.heading}
              />
              {heroData?.description && (
                <div
                  className={styles.description}
                  dangerouslySetInnerHTML={{ __html: heroData?.description }}
                ></div>
              )}
              {heroData?.button_link && (
                <Button
                  label={heroData?.button_title}
                  className={styles.cta}
                  onClick={() => {
                    const contactFormElement = document.getElementById(
                      `${heroData?.button_link}`,
                    );
                    if (contactFormElement) {
                      contactFormElement.scrollIntoView({ behavior: 'smooth' });
                    }
                  }}
                />
              )}
            </div>
          </div>
        </Container>
      )}
      {variant === 'cloud-migration' && (
        <Container fluid className={styles.main_container}>
          <ImageWithBlurPreview
            data={heroData?.image?.data?.attributes}
            mobileData={heroData?.mobile_image?.data?.attributes}
            fill={true}
            quality={95}
            priority={true}
            loading="eager"
            mainClass={styles.background_image}
          />
          <div className={styles.inner_container_ai_readiness}>
            <div className={styles.section}>
              <Heading
                headingType="h1"
                title={heroData?.title}
                className={styles.heading}
              />
              {heroData?.description && (
                <div
                  className={styles.description}
                  dangerouslySetInnerHTML={{ __html: heroData?.description }}
                ></div>
              )}
              {heroData?.button_link && (
                <Button
                  label={heroData?.button_title}
                  className={styles.ctaCloudMigration}
                  onClick={() => {
                    const contactFormElement = document.getElementById(
                      `${heroData?.button_link}`,
                    );
                    if (contactFormElement) {
                      contactFormElement.scrollIntoView({ behavior: 'smooth' });
                    }
                  }}
                />
              )}
            </div>
          </div>
        </Container>
      )}
    </>
  );
}
